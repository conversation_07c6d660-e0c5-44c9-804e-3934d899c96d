import { useModel, history } from '@umijs/max';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Divider,
  message,
  Modal,
  List,
  Tag,
  Flex,
} from 'antd';
import {
  TeamOutlined,
  PlusOutlined,
  SettingOutlined,
  UserOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { TeamService } from '@/services';
import type { CreateTeamRequest, TeamDetailResponse } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 团队管理设置卡片组件
 *
 * 提供团队相关的管理功能，包括：
 * - 创建新团队
 * - 查看用户的团队列表
 * - 团队快速操作
 * - 团队设置入口
 *
 * 功能特点：
 * - 创建团队模态框
 * - 团队列表展示
 * - 快速导航到团队管理
 * - 响应式设计
 */
const TeamManagementCard: React.FC = () => {
  const [form] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const { initialState } = useModel('@@initialState');

  /**
   * 获取用户团队列表
   *
   * 在组件挂载时获取用户的团队列表
   */
  useEffect(() => {
    if (initialState?.currentUser) {
      fetchTeams();
    }
  }, [initialState?.currentUser]);

  /**
   * 获取团队列表数据
   */
  const fetchTeams = async () => {
    setTeamsLoading(true);
    try {
      const teamsData = await TeamService.getUserTeamsWithStats();
      setTeams(teamsData);
    } catch (error) {
      console.error('获取团队列表失败:', error);
    } finally {
      setTeamsLoading(false);
    }
  };

  /**
   * 创建团队处理函数
   *
   * 处理团队创建的完整流程：
   * 1. 表单验证
   * 2. API调用
   * 3. 更新团队列表
   * 4. 关闭模态框
   * 5. 用户反馈
   */
  const handleCreateTeam = async (values: CreateTeamRequest) => {
    setCreateLoading(true);
    try {
      await TeamService.createTeam(values);

      // 重新获取团队列表
      await fetchTeams();

      // 关闭模态框并重置表单
      setCreateModalVisible(false);
      form.resetFields();

      message.success('团队创建成功！');
    } catch (error) {
      // 错误处理由响应拦截器统一处理
      console.error('创建团队失败:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  /**
   * 跳转到团队管理页面
   */
  const handleGoToTeamManagement = () => {
    history.push('/team-management');
  };

  /**
   * 跳转到个人中心查看团队
   */
  const handleGoToPersonalCenter = () => {
    history.push('/personal-center');
  };

  return (
    <>
      <Card
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
          border: 'none',
          background: 'linear-gradient(145deg, #ffffff, #f8faff)',
          height: '100%',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <TeamOutlined
              style={{
                fontSize: '24px',
                color: '#1890ff',
                padding: '8px',
                backgroundColor: '#e6f7ff',
                borderRadius: '8px',
              }}
            />
            <div>
              <Title
                level={4}
                style={{
                  margin: 0,
                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: 600,
                }}
              >
                团队管理
              </Title>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                创建和管理您的团队
              </Text>
            </div>
          </div>
        }
      >
        {/* 创建团队按钮 */}
        <div style={{ marginBottom: 24 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
            size="large"
            block
            style={{
              borderRadius: 8,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              border: 'none',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              height: '48px',
              fontSize: '16px',
              fontWeight: 600,
            }}
          >
            创建新团队
          </Button>
        </div>

        <Divider style={{ margin: '24px 0' }}>
          <Text type="secondary">我的团队</Text>
        </Divider>

        {/* 团队列表 */}
        <List
          loading={teamsLoading}
          dataSource={teams.slice(0, 3)} // 只显示前3个团队
          renderItem={(team) => (
            <List.Item
              style={{
                padding: '12px 0',
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <List.Item.Meta
                avatar={
                  <div
                    style={{
                      width: 40,
                      height: 40,
                      borderRadius: 8,
                      background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  >
                    {team.name.charAt(0).toUpperCase()}
                  </div>
                }
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Text strong style={{ fontSize: '14px' }}>
                      {team.name}
                    </Text>
                    {team.isCreator && (
                      <Tag color="blue" style={{ fontSize: '10px' }}>
                        创建者
                      </Tag>
                    )}
                  </div>
                }
                description={
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {team.memberCount} 名成员
                  </Text>
                }
              />
            </List.Item>
          )}
          locale={{ emptyText: '暂无团队，创建您的第一个团队吧！' }}
        />

        {teams.length > 3 && (
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              还有 {teams.length - 3} 个团队...
            </Text>
          </div>
        )}

        <Divider style={{ margin: '24px 0' }} />

        {/* 快速操作按钮 */}
        <Space direction="vertical" style={{ width: '100%' }} size={12}>
          <Button
            icon={<EyeOutlined />}
            onClick={handleGoToPersonalCenter}
            block
            style={{ borderRadius: 6 }}
          >
            查看所有团队
          </Button>
          <Button
            icon={<SettingOutlined />}
            onClick={handleGoToTeamManagement}
            block
            style={{ borderRadius: 6 }}
          >
            团队管理设置
          </Button>
        </Space>
      </Card>

      {/* 创建团队模态框 */}
      <Modal
        title={
          <Flex align="center" gap={8}>
            <TeamOutlined style={{ color: '#1890ff' }} />
            <span>创建新团队</span>
          </Flex>
        }
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
        style={{ top: 100 }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTeam}
          autoComplete="off"
          style={{ marginTop: 24 }}
        >
          <Form.Item
            label="团队名称"
            name="name"
            rules={[
              { required: true, message: '请输入团队名称！' },
              { max: 100, message: '团队名称长度不能超过100字符！' },
              { min: 2, message: '团队名称至少需要2个字符！' },
            ]}
          >
            <Input placeholder="请输入团队名称" size="large" />
          </Form.Item>

          <Form.Item
            label="团队描述"
            name="description"
            rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}
          >
            <TextArea
              placeholder="请输入团队描述（可选）"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, marginTop: 32 }}>
            <Flex justify="end" gap={12}>
              <Button
                onClick={() => {
                  setCreateModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createLoading}
                style={{
                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                  border: 'none',
                }}
              >
                创建团队
              </Button>
            </Flex>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TeamManagementCard;

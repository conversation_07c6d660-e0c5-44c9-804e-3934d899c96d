/**
 * 用户管理相关 API 服务
 */

import type {
  UpdateUserProfileRequest,
  UserPersonalStatsResponse,
  UserProfileDetailResponse,
  UserProfileResponse,
} from '@/types/api';
import { apiRequest } from '@/utils/request';

/**
 * 用户服务类
 *
 * 提供用户相关的所有API接口，包括：
 * - 用户资料管理（查看、更新）
 * - 密码修改
 * - 用户统计信息
 * - 账户管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
export class UserService {
  /**
   * 获取当前用户资料
   *
   * 获取当前登录用户的基本资料信息，包括姓名、邮箱、电话等。
   * 需要有效的用户Token。
   *
   * @returns Promise<UserProfileResponse> 用户资料信息
   * @throws 当用户未登录或Token无效时抛出异常
   *
   * @example
   * ```typescript
   * const profile = await UserService.getUserProfile();
   * console.log('用户姓名:', profile.name);
   * console.log('用户邮箱:', profile.email);
   * ```
   */
  static async getUserProfile(): Promise<UserProfileResponse> {
    const response =
      await apiRequest.get<UserProfileResponse>('/users/profile');
    return response.data;
  }

  /**
   * 更新用户资料
   *
   * 更新当前用户的资料信息。可以更新姓名、电话、职位等信息。
   * 邮箱通常不允许修改，因为它是用户的唯一标识。
   *
   * @param data 用户资料更新请求参数
   * @param data.name 用户姓名
   * @param data.telephone 电话号码（可选）
   * @param data.position 职位（可选）
   * @returns Promise<UserProfileResponse> 更新后的用户资料
   * @throws 当数据验证失败或用户无权限时抛出异常
   *
   * @example
   * ```typescript
   * const updatedProfile = await UserService.updateUserProfile({
   *   name: '张三',
   *   telephone: '13800138000',
   *   position: '项目经理'
   * });
   * console.log('资料更新成功:', updatedProfile);
   * ```
   */
  static async updateUserProfile(
    data: UpdateUserProfileRequest,
  ): Promise<UserProfileResponse> {
    const response = await apiRequest.put<UserProfileResponse>(
      '/users/profile',
      data,
    );
    return response.data;
  }

  /**
   * 修改密码
   *
   * 修改当前用户的登录密码。需要提供当前密码进行验证。
   *
   * @param currentPassword 当前密码
   * @param newPassword 新密码（8-20位，包含字母和数字）
   * @returns Promise<void> 修改成功时resolve
   * @throws 当当前密码错误或新密码格式不正确时抛出异常
   *
   * @example
   * ```typescript
   * await UserService.changePassword('oldPassword123', 'newPassword456');
   * console.log('密码修改成功');
   * ```
   */
  static async changePassword(
    currentPassword: string,
    newPassword: string,
  ): Promise<void> {
    const data: UpdateUserProfileRequest = {
      currentPassword,
      newPassword,
    };

    const response = await apiRequest.put<void>('/users/profile', data);
    return response.data;
  }

  /**
   * 更新用户名
   */
  static async updateUserName(name: string): Promise<UserProfileResponse> {
    const data: UpdateUserProfileRequest = {
      name,
    };

    const response = await apiRequest.put<UserProfileResponse>(
      '/users/profile',
      data,
    );
    return response.data;
  }

  /**
   * 验证当前密码
   */
  static async validateCurrentPassword(password: string): Promise<boolean> {
    try {
      const response = await apiRequest.post<boolean>(
        '/users/validate-password',
        { password },
      );
      return response.data;
    } catch {
      return false;
    }
  }

  /**
   * 获取用户统计信息
   *
   * 获取用户的团队统计信息，包括总团队数、创建的团队数等。
   * 注意：当前返回模拟数据，等待后端实现专门的统计接口。
   *
   * @returns Promise<object> 用户统计信息
   * @returns Promise<object>.totalTeams 总团队数
   * @returns Promise<object>.createdTeams 创建的团队数
   * @returns Promise<object>.joinedTeams 加入的团队数
   * @returns Promise<object>.lastLoginTime 最后登录时间
   *
   * @example
   * ```typescript
   * const stats = await UserService.getUserStats();
   * console.log('总团队数:', stats.totalTeams);
   * console.log('创建的团队:', stats.createdTeams);
   * ```
   */
  static async getUserStats(): Promise<{
    totalTeams: number;
    createdTeams: number;
    joinedTeams: number;
    lastLoginTime: string;
  }> {
    // 这里可能需要后端提供专门的统计接口
    // 暂时返回模拟数据
    return {
      totalTeams: 0,
      createdTeams: 0,
      joinedTeams: 0,
      lastLoginTime: new Date().toISOString(),
    };
  }

  /**
   * 获取用户个人统计数据
   *
   * 获取用户在当前团队中的业务统计数据，包括车辆、人员、预警、告警等数量。
   * 这些数据用于个人中心的统计卡片显示。
   *
   * @returns Promise<UserPersonalStatsResponse> 个人统计数据
   * @throws 当用户未选择团队或无权限时抛出异常
   *
   * @example
   * ```typescript
   * const personalStats = await UserService.getUserPersonalStats();
   * console.log('车辆数量:', personalStats.vehicles);
   * console.log('人员数量:', personalStats.personnel);
   * console.log('预警数量:', personalStats.warnings);
   * console.log('告警数量:', personalStats.alerts);
   * ```
   */
  static async getUserPersonalStats(): Promise<UserPersonalStatsResponse> {
    const response = await apiRequest.get<UserPersonalStatsResponse>(
      '/users/personal-stats',
    );
    return response.data;
  }

  /**
   * 获取用户详细信息
   *
   * 获取用户的详细资料信息，包括基本信息、注册时间、最后登录信息等。
   * 比getUserProfile返回更多的详细信息，用于个人中心展示。
   *
   * @returns Promise<UserProfileDetailResponse> 用户详细信息
   * @throws 当用户未登录或Token无效时抛出异常
   *
   * @example
   * ```typescript
   * const detail = await UserService.getUserProfileDetail();
   * console.log('注册时间:', detail.registerDate);
   * console.log('最后登录:', detail.lastLoginTime);
   * console.log('团队数量:', detail.teamCount);
   * ```
   */
  static async getUserProfileDetail(): Promise<UserProfileDetailResponse> {
    const response = await apiRequest.get<UserProfileDetailResponse>(
      '/users/profile-detail',
    );
    return response.data;
  }



  /**
   * 删除用户账户
   */
  static async deleteAccount(password: string): Promise<void> {
    const response = await apiRequest.delete<void>('/users/account', {
      password,
    });
    return response.data;
  }
}

// 导出默认实例
export default UserService;

/**
 * 集成团队管理页面
 *
 * 功能特性：
 * - 统一的团队管理界面，包含多个功能模块
 * - 团队名称卡片显示，提升用户体验
 * - 合并的成员与邀请管理功能
 * - 团队设置功能
 * - 权限控制，只有团队创建者可以访问管理功能
 *
 * 模块组织：
 * - 团队成员与邀请管理：查看、添加、移除团队成员，管理邀请
 * - 团队设置：编辑团队信息、删除团队
 */

import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Alert,
  Spin,
  Typography,
  Space,
  Tag,
  Avatar,
  Button,
  Dropdown,
  Modal,
  Form,
  Input,
  message
} from 'antd';
import {
  TeamOutlined,
  InfoCircleOutlined,
  CrownOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { history } from '@umijs/max';

// 导入子组件
import TeamMemberManagement from './components/TeamMemberManagement';

// 导入服务和类型
import { TeamService } from '@/services/team';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const TeamManagementPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);

  // 团队编辑相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [form] = Form.useForm();

  // 团队删除相关状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    fetchTeamDetail();
  }, []);

  const fetchTeamDetail = async () => {
    try {
      setLoading(true);
      const detail = await TeamService.getCurrentTeamDetail();
      setTeamDetail(detail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      // 如果获取失败，可能是没有选择团队，跳转到个人中心页面
      history.push('/personal-center');
    } finally {
      setLoading(false);
    }
  };

  // 处理编辑团队
  const handleEditTeam = () => {
    if (!teamDetail) return;
    form.setFieldsValue({
      name: teamDetail.name,
      description: teamDetail.description || '',
    });
    setEditModalVisible(true);
  };

  // 保存团队信息
  const handleSaveTeam = async (values: UpdateTeamRequest) => {
    try {
      setUpdating(true);
      await TeamService.updateCurrentTeam(values);
      message.success('团队信息更新成功');
      setEditModalVisible(false);
      fetchTeamDetail();
    } catch (error) {
      console.error('更新团队失败:', error);
      message.error('更新团队失败');
    } finally {
      setUpdating(false);
    }
  };

  // 删除团队
  const handleDeleteTeam = async () => {
    if (deleteConfirmText !== teamDetail?.name) {
      message.error('请输入正确的团队名称');
      return;
    }

    try {
      setDeleting(true);
      await TeamService.deleteCurrentTeam();
      message.success('团队已删除');
      setDeleteModalVisible(false);
      // 删除成功后跳转到团队选择页面
      history.push('/user/team-select');
    } catch (error) {
      console.error('删除团队失败:', error);
      message.error('删除团队失败');
    } finally {
      setDeleting(false);
    }
  };

  // 权限检查：只有团队创建者可以访问管理功能
  const hasManagePermission = teamDetail?.isCreator || false;

  if (loading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">正在加载团队信息...</Text>
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!teamDetail) {
    return (
      <PageContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
            <Title level={4}>未找到团队信息</Title>
            <Text type="secondary">请先选择一个团队</Text>
            <div style={{ marginTop: 16 }}>
              <Button type="primary" onClick={() => history.push('/personal-center')}>
                返回个人中心
              </Button>
            </div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  // 权限不足提示
  if (!hasManagePermission) {
    return (
      <PageContainer>
        <Card>
          <Alert
            message="权限不足"
            description="只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。"
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={() => history.push('/dashboard')}>
                返回首页
              </Button>
            }
          />
        </Card>
      </PageContainer>
    );
  }



  // 操作菜单项
  const menuItems = [
    {
      key: 'edit',
      label: '编辑团队',
      icon: <EditOutlined />,
      onClick: handleEditTeam,
    },
    {
      key: 'delete',
      label: '删除团队',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => setDeleteModalVisible(true),
    },
  ];

  return (
    <PageContainer>
      {/* 团队名称卡片 */}
      <Card
        style={{ marginBottom: 16 }}
        extra={
          hasManagePermission && (
            <Dropdown
              menu={{ items: menuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                style={{ fontSize: 16 }}
              />
            </Dropdown>
          )
        }
      >
        <Space size="large" align="center">
          <Avatar
            size={48}
            icon={<TeamOutlined />}
            style={{
              backgroundColor: '#1890ff',
              fontSize: 20,
            }}
          />
          <div>
            <Title level={3} style={{ margin: 0, marginBottom: 4 }}>
              {teamDetail.name}
            </Title>
            <Space>
              {teamDetail.isCreator && (
                <Tag
                  icon={<CrownOutlined />}
                  color="gold"
                >
                  管理员
                </Tag>
              )}
              <Text type="secondary">
                {teamDetail.description || '这个团队还没有描述'}
              </Text>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 团队成员与邀请管理 */}
      <TeamMemberManagement
        teamDetail={teamDetail}
        onRefresh={fetchTeamDetail}
      />

      {/* 编辑团队模态框 */}
      <Modal
        title="编辑团队信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTeam}
        >
          <Form.Item
            name="name"
            label="团队名称"
            rules={[
              { required: true, message: '请输入团队名称' },
              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' },
            ]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="团队描述"
            rules={[
              { max: 200, message: '团队描述不能超过200个字符' },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入团队描述（可选）"
              showCount
              maxLength={200}
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setEditModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={updating}
                icon={<SaveOutlined />}
              >
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除团队确认模态框 */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            <Text type="danger">删除团队确认</Text>
          </Space>
        }
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeleteConfirmText('');
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="警告：此操作不可恢复"
          description={
            <div>
              <p>删除团队将会：</p>
              <ul>
                <li>永久删除团队及所有相关数据</li>
                <li>移除所有团队成员</li>
                <li>清除团队设置和配置</li>
                <li>无法恢复任何数据</li>
              </ul>
            </div>
          }
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <div style={{ marginBottom: 16 }}>
          <Text strong>
            请输入团队名称 "<Text code>{teamDetail?.name}</Text>" 来确认删除：
          </Text>
        </div>

        <Input
          placeholder={`请输入：${teamDetail?.name}`}
          value={deleteConfirmText}
          onChange={(e) => setDeleteConfirmText(e.target.value)}
          style={{ marginBottom: 24 }}
        />

        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button
              onClick={() => {
                setDeleteModalVisible(false);
                setDeleteConfirmText('');
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              danger
              loading={deleting}
              disabled={deleteConfirmText !== teamDetail?.name}
              onClick={handleDeleteTeam}
              icon={<DeleteOutlined />}
            >
              确认删除团队
            </Button>
          </Space>
        </div>
      </Modal>
    </PageContainer>
  );
};

export default TeamManagementPage;
